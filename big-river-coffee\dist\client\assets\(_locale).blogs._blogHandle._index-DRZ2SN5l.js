import{w as l}from"./with-props-ZU_SrDpf.js";import{j as o}from"./jsx-runtime-D5QEUsP9.js";import{u as r,L as a}from"./chunk-D4RADZKF-DNKU_WG6.js";import{P as m}from"./PaginatedResourceSection-DEx_qoAZ.js";import{I as d}from"./Image-BhFgnyoc.js";import"./index-AZaPwvne.js";const j=({data:e})=>[{title:`Hydrogen | ${(e==null?void 0:e.blog.title)??""} blog`}],f=l(function(){const{blog:s}=r(),{articles:n}=s;return o.jsxs("div",{className:"blog",children:[o.jsx("h1",{children:s.title}),o.jsx("div",{className:"blog-grid",children:o.jsx(m,{connection:n,children:({node:i,index:t})=>o.jsx(c,{article:i,loading:t<2?"eager":"lazy"},i.id)})})]})});function c({article:e,loading:s}){const n=new Intl.DateTimeFormat("en-US",{year:"numeric",month:"long",day:"numeric"}).format(new Date(e.publishedAt));return o.jsx("div",{className:"blog-article",children:o.jsxs(a,{to:`/blogs/${e.blog.handle}/${e.handle}`,children:[e.image&&o.jsx("div",{className:"blog-article-image",children:o.jsx(d,{alt:e.image.altText||e.title,aspectRatio:"3/2",data:e.image,loading:s,sizes:"(min-width: 768px) 50vw, 100vw"})}),o.jsx("h3",{children:e.title}),o.jsx("small",{children:n})]})},e.id)}export{f as default,j as meta};
