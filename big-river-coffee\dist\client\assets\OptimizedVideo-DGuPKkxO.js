import{j as a}from"./jsx-runtime-D5QEUsP9.js";import{r as s}from"./chunk-D4RADZKF-DNKU_WG6.js";function A({src:V,fallbackImage:p,poster:E,className:m="",style:g={},autoPlay:i=!0,muted:O=!0,loop:R=!0,playsInline:T=!0,preload:$="metadata",lazy:o=!0,onLoadStart:l,onCanPlay:f,onError:v}){const t=s.useRef(null),[d,F]=s.useState(!o),[c,L]=s.useState(!1),[H,M]=s.useState(!1),[j,u]=s.useState(!1),[D,N]=s.useState(!1);if(s.useEffect(()=>{if(!o||d)return;const e=new IntersectionObserver(r=>{r.forEach(n=>{n.isIntersecting&&(F(!0),e.disconnect())})},{rootMargin:"50px",threshold:.1});return t.current&&e.observe(t.current),()=>e.disconnect()},[o,d]),s.useEffect(()=>{const e=t.current;if(!e)return;const r=()=>{l==null||l()},n=()=>{L(!0),f==null||f()},x=()=>{N(!0)},y=()=>{L(!0)},b=()=>{M(!0),v==null||v()},w=()=>{u(!0)},I=()=>{u(!1)};return e.addEventListener("loadstart",r),e.addEventListener("canplay",n),e.addEventListener("canplaythrough",x),e.addEventListener("loadeddata",y),e.addEventListener("error",b),e.addEventListener("play",w),e.addEventListener("pause",I),()=>{e.removeEventListener("loadstart",r),e.removeEventListener("canplay",n),e.removeEventListener("canplaythrough",x),e.removeEventListener("loadeddata",y),e.removeEventListener("error",b),e.removeEventListener("play",w),e.removeEventListener("pause",I)}},[l,f,v]),s.useEffect(()=>{if(c&&i&&t.current&&!j){const e=t.current.play();e!==void 0&&e.catch(r=>{console.warn("Video autoplay failed:",r),u(!1)})}},[c,i,j]),s.useEffect(()=>{if(!o&&t.current&&d){const e=t.current;if(e.load(),i){const r=e.play();r!==void 0&&r.catch(n=>{console.warn("Initial video autoplay failed:",n)})}}},[o,i,d]),s.useEffect(()=>{if(!t.current)return;const e=new IntersectionObserver(r=>{r.forEach(n=>{t.current&&(n.isIntersecting?i&&c&&(t.current.play().catch(()=>{}),u(!0)):(t.current.pause(),u(!1)))})},{threshold:.3,rootMargin:"50px"});return e.observe(t.current),()=>e.disconnect()},[i,c]),H&&p)return a.jsx("img",{src:p,alt:"Video fallback",className:m,style:g});const h=E||p;return a.jsxs("div",{className:"relative",style:g,children:[!c&&h&&a.jsx("img",{src:h,alt:"Loading...",className:`${m} absolute inset-0 w-full h-full object-cover z-10`,style:{objectFit:"cover"},loading:"eager"}),d&&a.jsxs("video",{ref:t,className:`${m} ${c?"opacity-100":"opacity-0"} transition-opacity duration-500 ease-in-out`,autoPlay:i,muted:O,loop:R,playsInline:T,preload:$,poster:E,style:{objectFit:"cover"},children:[a.jsx("source",{src:V,type:"video/mp4"}),h&&a.jsx("img",{src:h,alt:"Video not supported",className:"w-full h-full object-cover"})]})]})}export{A as O};
