import{w as y}from"./with-props-ZU_SrDpf.js";import{j as e}from"./jsx-runtime-D5QEUsP9.js";import{r as u,c as L,L as M,u as I,R as P}from"./chunk-D4RADZKF-DNKU_WG6.js";import{q as S,s as C}from"./index-BgHXxrvA.js";import{P as $}from"./ProductPrice-C6YWsE4Q.js";import{I as A}from"./Image-BhFgnyoc.js";import{u as E}from"./Aside-HLZLr7KT.js";import{S as H,u as R}from"./SubscriptionDropdown-DC4IQEjY.js";import{g as _,a as O}from"./getProductOptions-DiFA5z_h.js";import"./Money-BAfw1cBI.js";import"./AddToCartButton-D0pKxfbC.js";function w({image:s,priority:t=!1,className:a="",sizes:c="(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 25vw",aspectRatio:r="1/1",loading:n="lazy"}){const[x,p]=u.useState(t),[h,o]=u.useState(!1),d=u.useRef(null);return u.useEffect(()=>{if(t||x||!d.current)return;const l=new IntersectionObserver(m=>{m.forEach(b=>{b.isIntersecting&&(p(!0),l.disconnect())})},{rootMargin:"100px",threshold:.1});return l.observe(d.current),()=>l.disconnect()},[t,x]),u.useEffect(()=>{if(t&&(s!=null&&s.url)){const l=document.createElement("link");l.rel="preload",l.as="image",l.href=`${s.url}&width=640&format=webp`,document.head.appendChild(l)}},[t,s==null?void 0:s.url]),s?!x&&!t?e.jsx("div",{ref:d,className:`product-image bg-gray-100 animate-pulse ${a}`,style:{aspectRatio:r}}):e.jsxs("div",{ref:d,className:`product-image relative overflow-hidden ${a}`,children:[!h&&e.jsx("div",{className:"absolute inset-0 bg-gray-100 animate-pulse",style:{aspectRatio:r}}),e.jsx(A,{alt:s.altText||"Product Image",aspectRatio:r,data:s,sizes:c,loading:t?"eager":n,className:`transition-opacity duration-300 ${h?"opacity-100":"opacity-0"}`,onLoad:()=>o(!0)},s.id)]}):e.jsx("div",{className:`product-image bg-gray-100 animate-pulse ${a}`,style:{aspectRatio:r}})}function z({image:s}){return e.jsx(w,{image:s,priority:!0,sizes:"(max-width: 768px) 100vw, 50vw",className:"w-full h-full",aspectRatio:"1/1",loading:"eager"})}function B({image:s,isActive:t=!1}){return e.jsx(w,{image:s,priority:t,sizes:"100px",className:`w-full h-full ${t?"ring-2 ring-blue-500":""}`,aspectRatio:"1/1"})}function W({productOptions:s,selectedVariant:t,product:a}){const c=L(),{open:r}=E(),[n,x]=u.useState(1),[p,h]=u.useState(null);return e.jsxs("div",{className:"product-form space-y-6",children:[s.map(o=>o.optionValues.length===1?null:e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"text-sm font-medium !text-black",children:o.name}),e.jsx("div",{className:"flex flex-wrap gap-2",children:o.optionValues.map(d=>{const{name:l,handle:m,variantUriQuery:b,selected:g,available:N,exists:f,isDifferentProduct:k,swatch:i}=d,j=`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${g?"bg-army-600 text-white shadow-sm":"bg-white !text-black hover:bg-gray-200 border border-gray-300"} ${!N||!f?"opacity-40 cursor-not-allowed":""}`;return k?e.jsx(M,{className:j,prefetch:"intent",preventScrollReset:!0,replace:!0,to:`/products/${m}?${b}`,children:i!=null&&i.color||i!=null&&i.image?e.jsx(v,{swatch:i,name:l}):l},o.name+l):e.jsx("button",{type:"button",className:j,disabled:!f,onClick:()=>{g||c(`?${b}`,{replace:!0,preventScrollReset:!0})},children:i!=null&&i.color||i!=null&&i.image?e.jsx(v,{swatch:i,name:l}):l},o.name+l)})})]},o.name)),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"text-sm font-medium !text-black",children:"Quantity"}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("button",{type:"button",onClick:()=>x(Math.max(1,n-1)),className:"w-10 h-10 rounded-md border border-gray-300 bg-white flex items-center justify-center hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",disabled:n<=1,children:e.jsx("svg",{className:"w-4 h-4 !text-black",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20 12H4"})})}),e.jsx("span",{className:"w-12 text-center font-medium !text-black bg-white px-2 py-1 rounded",children:n}),e.jsx("button",{type:"button",onClick:()=>x(n+1),className:"w-10 h-10 rounded-md border border-gray-300 bg-white flex items-center justify-center hover:bg-gray-50 transition-colors",children:e.jsx("svg",{className:"w-4 h-4 !text-black",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})})})]})]}),a&&t&&e.jsx(H,{product:a,selectedVariant:t,quantity:n})]})}function v({swatch:s,name:t}){var r,n;const a=(n=(r=s==null?void 0:s.image)==null?void 0:r.previewImage)==null?void 0:n.url,c=s==null?void 0:s.color;return!a&&!c?t:e.jsx("div",{"aria-label":t,className:"product-option-label-swatch",style:{backgroundColor:c||"transparent"},children:!!a&&e.jsx("img",{src:a,alt:t})})}const Y=({data:s})=>[{title:`Hydrogen | ${(s==null?void 0:s.product.title)??""}`},{rel:"canonical",href:`/products/${s==null?void 0:s.product.handle}`}],Z=y(function(){var h,o,d;const{product:t}=I(),[a,c]=P.useState(0),r=S(t.selectedOrFirstAvailableVariant,_(t));R(r.selectedOptions);const n=O({...t,selectedOrFirstAvailableVariant:r}),{title:x,descriptionHtml:p}=t;return e.jsxs("div",{className:"min-h-screen product-page-content",style:{backgroundImage:"url(/newhomepage/mobile_homeage_bg_sf.webp), url(/newhomepage/mobile_homeage_bg_sf.png)",backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"},children:[e.jsx("div",{className:"relative bg-gradient-to-br from-[#3a5c5c]/90 to-[#2d4747]/90 text-white",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 lg:py-24",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center",children:[e.jsx("div",{className:"space-y-6",children:((o=(h=t.images)==null?void 0:h.nodes)==null?void 0:o.length)>0?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"relative group",children:[e.jsx("div",{className:"aspect-square bg-white/10 rounded-2xl overflow-hidden backdrop-blur-sm border border-white/20",children:e.jsx(z,{image:t.images.nodes[a]||t.images.nodes[0],alt:((d=t.images.nodes[a])==null?void 0:d.altText)||t.images.nodes[0].altText||t.title,className:"w-full h-full object-center object-cover transition-transform duration-500 group-hover:scale-105"})}),e.jsx("div",{className:"absolute top-4 left-4 bg-[#db8027] text-white px-3 py-1 rounded-full text-sm font-medium",children:"Nicaragua Origin"})]}),t.images.nodes.length>1&&e.jsx("div",{className:"grid grid-cols-4 gap-3",children:t.images.nodes.map((l,m)=>e.jsx("div",{className:`aspect-square bg-white/10 rounded-lg overflow-hidden backdrop-blur-sm border transition-all duration-300 cursor-pointer ${a===m?"border-[#db8027] border-2 ring-2 ring-[#db8027]/50":"border-white/20 hover:border-white/40"}`,onClick:()=>c(m),children:e.jsx(B,{image:l,alt:l.altText||`${t.title} ${m+1}`,className:"w-full h-full object-center object-cover hover:scale-105 transition-transform duration-300",isActive:a===m})},l.id))})]}):e.jsx("div",{className:"aspect-square bg-white/10 rounded-2xl flex items-center justify-center backdrop-blur-sm border border-white/20",children:e.jsxs("div",{className:"text-center",children:[e.jsx("svg",{className:"w-16 h-16 text-white/60 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),e.jsx("span",{className:"text-white/60",children:"No image available"})]})})}),e.jsxs("div",{className:"space-y-8",children:[e.jsxs("nav",{className:"flex items-center space-x-2 text-sm text-white/70",children:[e.jsx("a",{href:"/collections/all",className:"hover:text-white transition-colors",children:"Coffee"}),e.jsx("span",{className:"text-white",children:"→"}),e.jsx("span",{className:"text-white",children:t.title})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-[#db8027] mb-2 uppercase tracking-wider text-sm font-semibold",children:t.vendor}),e.jsx("h1",{className:"text-3xl lg:text-4xl font-bold leading-tight mb-4 text-white",children:x}),e.jsxs("div",{className:"flex flex-wrap gap-2 mb-6",children:[e.jsx("span",{className:"bg-white/20 text-white px-3 py-1 rounded-full text-sm backdrop-blur-sm",children:"Premium Roast"}),e.jsx("span",{className:"bg-white/20 text-white px-3 py-1 rounded-full text-sm backdrop-blur-sm",children:"Single Origin"}),e.jsx("span",{className:"bg-white/20 text-white px-3 py-1 rounded-full text-sm backdrop-blur-sm",children:"Adventure Ready"})]})]}),e.jsx("div",{className:"text-2xl lg:text-3xl font-bold text-[#db8027]",children:e.jsx($,{price:r==null?void 0:r.price,compareAtPrice:r==null?void 0:r.compareAtPrice})}),e.jsx("div",{className:"bg-white/90 backdrop-blur-sm rounded-xl p-6 border border-white/30 shadow-lg",children:e.jsx(W,{productOptions:n,selectedVariant:r,product:t})}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-10 h-10 bg-[#db8027] rounded-full flex items-center justify-center",children:e.jsx("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"font-semibold text-white",children:"Expert Roasted"}),e.jsx("p",{className:"text-sm text-white/70",children:"Small batch perfection"})]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-10 h-10 bg-[#db8027] rounded-full flex items-center justify-center",children:e.jsx("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"font-semibold text-white",children:"Ethically Sourced"}),e.jsx("p",{className:"text-sm text-white/70",children:"Direct trade partners"})]})]})]})]})]})})}),e.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:e.jsxs("div",{className:"bg-white rounded-2xl shadow-xl p-8 lg:p-12",children:[e.jsx("h2",{className:"text-2xl lg:text-3xl font-bold text-[#3a5c5c] mb-6 text-center",children:"About This Coffee"}),e.jsx("div",{className:"w-16 h-1 bg-[#db8027] mx-auto mb-8"}),e.jsx("div",{className:"text-gray-700 leading-relaxed prose prose-lg max-w-none text-center",dangerouslySetInnerHTML:{__html:p}})]})}),e.jsx("div",{className:"py-20 bg-gradient-to-r from-[#3a5c5c] to-[#2d4747]",children:e.jsxs("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"text-center mb-16",children:[e.jsx("h2",{className:"text-3xl lg:text-4xl font-bold text-white mb-4",children:"Our Adventure Promise"}),e.jsx("div",{className:"w-20 h-1 bg-[#db8027] mx-auto mb-6"}),e.jsx("p",{className:"adventure-promise-text text-white/80 text-lg max-w-2xl mx-auto px-4 text-center",children:"Every cup is crafted to fuel your next adventure, sourced with care and roasted to perfection."})]}),e.jsxs("div",{className:"grid md:grid-cols-3 gap-8 mb-12",children:[e.jsxs("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-8 text-center border border-white/20 hover:bg-white/15 transition-all duration-300",children:[e.jsx("div",{className:"w-16 h-16 bg-[#db8027] rounded-full flex items-center justify-center mb-6 mx-auto",children:e.jsx("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),e.jsx("h3",{className:"text-xl font-bold text-white mb-3",children:"Ethically Sourced"}),e.jsx("p",{className:"text-white/70",children:"Direct partnerships with mountain farmers in Nicaragua, ensuring fair trade and sustainable practices."})]}),e.jsxs("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-8 text-center border border-white/20 hover:bg-white/15 transition-all duration-300",children:[e.jsx("div",{className:"w-16 h-16 bg-[#db8027] rounded-full flex items-center justify-center mb-6 mx-auto",children:e.jsx("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"})})}),e.jsx("h3",{className:"text-xl font-bold text-white mb-3",children:"Expert Roasted"}),e.jsx("p",{className:"text-white/70",children:"Small-batch roasting by master craftsmen, bringing out the unique flavors of each origin."})]}),e.jsxs("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-8 text-center border border-white/20 hover:bg-white/15 transition-all duration-300",children:[e.jsx("div",{className:"w-16 h-16 bg-[#db8027] rounded-full flex items-center justify-center mb-6 mx-auto",children:e.jsx("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),e.jsx("h3",{className:"text-xl font-bold text-white mb-3",children:"Adventure Ready"}),e.jsx("p",{className:"text-white/70",children:"Perfectly crafted to fuel any adventure, from mountain peaks to morning commutes."})]})]}),e.jsx("div",{className:"text-center",children:e.jsxs("a",{href:"/collections/all",className:"inline-flex items-center px-8 py-4 bg-[#db8027] hover:bg-[#c4721f] text-white rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-xl",children:["Explore All Coffee",e.jsx("svg",{className:"ml-2 w-5 h-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M14 5l7 7m0 0l-7 7m7-7H3"})})]})})]})}),e.jsx(C.ProductView,{data:{products:[{id:t.id,title:t.title,price:(r==null?void 0:r.price.amount)||"0",vendor:t.vendor,variantId:(r==null?void 0:r.id)||"",variantTitle:(r==null?void 0:r.title)||"",quantity:1}]}})]})});export{Z as default,Y as meta};
