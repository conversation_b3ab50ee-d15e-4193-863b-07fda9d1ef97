import type {ProductVariantFragment} from 'storefrontapi.generated';
import {Image} from '@shopify/hydrogen';
import { useState, useRef, useEffect } from 'react';

interface OptimizedProductImageProps {
  image: ProductVariantFragment['image'];
  priority?: boolean;
  className?: string;
  sizes?: string;
  aspectRatio?: string;
  loading?: 'eager' | 'lazy';
}

export function ProductImage({
  image,
  priority = false,
  className = '',
  sizes = "(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 25vw",
  aspectRatio = "1/1",
  loading = 'lazy',
}: OptimizedProductImageProps) {
  const [isInView, setIsInView] = useState(priority);
  const [isLoaded, setIsLoaded] = useState(false);
  const imgRef = useRef<HTMLDivElement>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority || isInView || !imgRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observer.disconnect();
          }
        });
      },
      {
        rootMargin: '100px', // Start loading 100px before entering viewport
        threshold: 0.1,
      }
    );

    observer.observe(imgRef.current);
    return () => observer.disconnect();
  }, [priority, isInView]);

  // Preload critical images
  useEffect(() => {
    if (priority && image?.url) {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = `${image.url}&width=640&format=webp`;
      document.head.appendChild(link);
    }
  }, [priority, image?.url]);

  if (!image) {
    return (
      <div
        className={`product-image bg-gray-100 animate-pulse ${className}`}
        style={{ aspectRatio }}
      />
    );
  }

  // Show placeholder until in view
  if (!isInView && !priority) {
    return (
      <div
        ref={imgRef}
        className={`product-image bg-gray-100 animate-pulse ${className}`}
        style={{ aspectRatio }}
      />
    );
  }

  return (
    <div
      ref={imgRef}
      className={`product-image relative overflow-hidden ${className}`}
    >
      {/* Loading placeholder */}
      {!isLoaded && (
        <div
          className="absolute inset-0 bg-gray-100 animate-pulse"
          style={{ aspectRatio }}
        />
      )}

      <Image
        alt={image.altText || 'Product Image'}
        aspectRatio={aspectRatio}
        data={image}
        key={image.id}
        sizes={sizes}
        loading={priority ? 'eager' : loading}
        className={`transition-opacity duration-300 ${
          isLoaded ? 'opacity-100' : 'opacity-0'
        }`}
        onLoad={() => setIsLoaded(true)}
      />
    </div>
  );
}

// Specialized components for different use cases
export function ProductGridImage({
  image,
  priority = false,
}: {
  image: ProductVariantFragment['image'];
  priority?: boolean;
}) {
  return (
    <ProductImage
      image={image}
      priority={priority}
      sizes="(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 25vw"
      className="w-full h-full"
      aspectRatio="1/1"
    />
  );
}

export function ProductHeroImage({
  image,
}: {
  image: ProductVariantFragment['image'];
}) {
  return (
    <ProductImage
      image={image}
      priority={true}
      sizes="(max-width: 768px) 100vw, 50vw"
      className="w-full h-full"
      aspectRatio="1/1"
      loading="eager"
    />
  );
}

export function ProductThumbnailImage({
  image,
  isActive = false,
}: {
  image: ProductVariantFragment['image'];
  isActive?: boolean;
}) {
  return (
    <ProductImage
      image={image}
      priority={isActive}
      sizes="100px"
      className={`w-full h-full ${isActive ? 'ring-2 ring-blue-500' : ''}`}
      aspectRatio="1/1"
    />
  );
}
