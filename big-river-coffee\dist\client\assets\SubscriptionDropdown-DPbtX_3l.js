import{r as c}from"./chunk-D4RADZKF-DNKU_WG6.js";import{m as x}from"./getProductOptions-DiFA5z_h.js";import{j as e}from"./jsx-runtime-D5QEUsP9.js";import{A as u}from"./AddToCartButton-0GlraiGn.js";import{u as p}from"./Aside-HLZLr7KT.js";function S(r){return c.useEffect(()=>{const s=new URLSearchParams(x(r||[])),i=new URLSearchParams(window.location.search),l=new URLSearchParams({...Object.fromEntries(i),...Object.fromEntries(s)});l.size>0&&window.history.replaceState({},"",`${window.location.pathname}?${l.toString()}`)},[JSON.stringify(r)]),null}function N({product:r,selectedVariant:s,quantity:i}){var h;const{open:l}=p(),[a,f]=c.useState("onetime"),[t,m]=c.useState(!1),d=[{id:"onetime",name:"One-time purchase",description:"No commitment",sellingPlanId:""},{id:"weekly",name:"Weekly",description:"Delivered every week • 15% off",sellingPlanId:"gid://shopify/SellingPlan/9581953339"},{id:"monthly",name:"Monthly",description:"Delivered every month • 15% off",sellingPlanId:"gid://shopify/SellingPlan/9581986107"},{id:"every3weeks",name:"Every 3 weeks",description:"Delivered every 3 weeks • 15% off",sellingPlanId:"gid://shopify/SellingPlan/9582018875"},{id:"every6weeks",name:"Every 6 weeks",description:"Delivered every 6 weeks • 15% off",sellingPlanId:"gid://shopify/SellingPlan/9582051643"}],o=d.find(n=>n.id===a)||d[0];return((h=r==null?void 0:r.sellingPlanGroups)==null?void 0:h.nodes)&&r.sellingPlanGroups.nodes.length>0?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-sm font-medium !text-black",children:"Subscription Frequency"}),e.jsx("span",{className:"text-xs font-medium text-orange-600 bg-orange-50 px-2 py-1 rounded-full",children:"15% OFF"})]}),e.jsxs("div",{className:"relative",children:[e.jsx("button",{type:"button",onClick:()=>m(!t),className:"w-full bg-white border border-gray-300 rounded-lg px-4 py-3 text-left shadow-sm focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 hover:border-gray-400 transition-colors duration-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("div",{className:"flex items-center",children:e.jsx("span",{className:"font-medium !text-black",children:o.name})}),e.jsx("span",{className:"text-sm !text-gray-700",children:o.description})]}),e.jsx("svg",{className:`w-5 h-5 text-gray-400 transition-transform duration-200 ${t?"transform rotate-180":""}`,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]})}),t&&e.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto overscroll-contain",children:d.map(n=>e.jsx("button",{type:"button",onClick:()=>{f(n.id),m(!1)},className:`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-200 first:rounded-t-lg last:rounded-b-lg ${a===n.id?"bg-army-50 border-l-4 border-army-500":""}`,children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("div",{className:"flex items-center",children:e.jsx("span",{className:"font-medium !text-black",children:n.name})}),e.jsx("span",{className:"text-sm !text-gray-700",children:n.description})]}),a===n.id&&e.jsx("svg",{className:"w-5 h-5 text-army-500",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})]})},n.id))})]})]}),e.jsx("div",{className:"pt-4",children:e.jsx(u,{disabled:!s||!s.availableForSale,onClick:()=>{l("cart")},lines:s?[{merchandiseId:s.id,quantity:i,selectedVariant:s,...o.sellingPlanId&&{sellingPlanId:o.sellingPlanId}}]:[],className:"w-full bg-army-600 hover:bg-army-700 font-medium py-3 px-4 rounded-lg transition-colors duration-200 text-white",children:s!=null&&s.availableForSale?"Add to Cart":"Sold out"})})]}):e.jsx("div",{className:"pt-4",children:e.jsx(u,{disabled:!s||!s.availableForSale,onClick:()=>{l("cart")},lines:s?[{merchandiseId:s.id,quantity:i,selectedVariant:s}]:[],children:s!=null&&s.availableForSale?"Add to cart":"Sold out"})})}export{N as S,S as u};
