import {redirect, type LoaderFunctionArgs} from '@shopify/remix-oxygen';
import {useLoaderData, type MetaFunction} from 'react-router';
import {
  getSelectedProductOptions,
  Analytics,
  useOptimisticVariant,
  getProductOptions,
  getAdjacentAndFirstAvailableVariants,
  useSelectedOptionInUrlParam,
} from '@shopify/hydrogen';
import {ProductPrice} from '~/components/ProductPrice';
import {ProductHeroImage, ProductThumbnailImage} from '~/components/ProductImage';
import {ProductForm} from '~/components/ProductForm';
import {redirectIfHandleIsLocalized} from '~/lib/redirect';
import React from 'react';

export const meta: MetaFunction<typeof loader> = ({data}) => {
  return [
    {title: `Hydrogen | ${data?.product.title ?? ''}`},
    {
      rel: 'canonical',
      href: `/products/${data?.product.handle}`,
    },
  ];
};

export async function loader(args: LoaderFunctionArgs) {
  // Start fetching non-critical data without blocking time to first byte
  const deferredData = loadDeferredData(args);

  // Await the critical data required to render initial state of the page
  const criticalData = await loadCriticalData(args);

  return {...deferredData, ...criticalData};
}

/**
 * Load data necessary for rendering content above the fold. This is the critical data
 * needed to render the page. If it's unavailable, the whole page should 400 or 500 error.
 */
async function loadCriticalData({
  context,
  params,
  request,
}: LoaderFunctionArgs) {
  const {handle} = params;
  const {storefront} = context;

  if (!handle) {
    throw new Error('Expected product handle to be defined');
  }

  const [{product}] = await Promise.all([
    storefront.query(PRODUCT_QUERY, {
      variables: {handle, selectedOptions: getSelectedProductOptions(request)},
      cache: storefront.CacheLong(), // Aggressive caching for product data
    }),
    // Add other queries here, so that they are loaded in parallel
  ]);

  if (!product?.id) {
    throw new Response(null, {status: 404});
  }

  // The API handle might be localized, so redirect to the localized handle
  redirectIfHandleIsLocalized(request, {handle, data: product});

  return {
    product,
  };
}

/**
 * Load data for rendering content below the fold. This data is deferred and will be
 * fetched after the initial page load. If it's unavailable, the page should still 200.
 * Make sure to not throw any errors here, as it will cause the page to 500.
 */
function loadDeferredData({context, params}: LoaderFunctionArgs) {
  // Put any API calls that is not critical to be available on first page render
  // For example: product reviews, product recommendations, social feeds.

  return {};
}

export default function Product() {
  const {product} = useLoaderData<typeof loader>();

  // State for selected image
  const [selectedImageIndex, setSelectedImageIndex] = React.useState(0);

  // Optimistically selects a variant with given available variant information
  const selectedVariant = useOptimisticVariant(
    product.selectedOrFirstAvailableVariant,
    getAdjacentAndFirstAvailableVariants(product),
  );

  // Sets the search param to the selected variant without navigation
  // only when no search params are set in the url
  useSelectedOptionInUrlParam(selectedVariant.selectedOptions);

  // Get the product options array
  const productOptions = getProductOptions({
    ...product,
    selectedOrFirstAvailableVariant: selectedVariant,
  });

  const {title, descriptionHtml} = product;

  return (
    <div className="min-h-screen product-page-content" style={{
      backgroundImage: 'url(/newhomepage/mobile_homeage_bg_sf.webp), url(/newhomepage/mobile_homeage_bg_sf.png)',
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat'
    }}>
      {/* Hero Section with Product */}
      <div className="relative bg-gradient-to-br from-[#3a5c5c]/90 to-[#2d4747]/90 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 lg:py-24">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">

            {/* Product Images */}
            <div className="space-y-6">
              {product.images?.nodes?.length > 0 ? (
                <>
                  <div className="relative group">
                    <div className="aspect-square bg-white/10 rounded-2xl overflow-hidden backdrop-blur-sm border border-white/20">
                      <ProductHeroImage
                        image={product.images.nodes[selectedImageIndex] || product.images.nodes[0]}
                        alt={product.images.nodes[selectedImageIndex]?.altText || product.images.nodes[0].altText || product.title}
                        className="w-full h-full object-center object-cover transition-transform duration-500 group-hover:scale-105"
                      />
                    </div>

                    {/* Image overlay with coffee origin badge */}
                    <div className="absolute top-4 left-4 bg-[#db8027] text-white px-3 py-1 rounded-full text-sm font-medium">
                      Nicaragua Origin
                    </div>
                  </div>

                  {product.images.nodes.length > 1 && (
                    <div className="grid grid-cols-4 gap-3">
                      {product.images.nodes.map((image, index) => (
                        <div
                          key={image.id}
                          className={`aspect-square bg-white/10 rounded-lg overflow-hidden backdrop-blur-sm border transition-all duration-300 cursor-pointer ${
                            selectedImageIndex === index
                              ? 'border-[#db8027] border-2 ring-2 ring-[#db8027]/50'
                              : 'border-white/20 hover:border-white/40'
                          }`}
                          onClick={() => setSelectedImageIndex(index)}
                        >
                          <ProductThumbnailImage
                            image={image}
                            alt={image.altText || `${product.title} ${index + 1}`}
                            className="w-full h-full object-center object-cover hover:scale-105 transition-transform duration-300"
                            isActive={selectedImageIndex === index}
                          />
                        </div>
                      ))}
                    </div>
                  )}
                </>
              ) : (
                <div className="aspect-square bg-white/10 rounded-2xl flex items-center justify-center backdrop-blur-sm border border-white/20">
                  <div className="text-center">
                    <svg className="w-16 h-16 text-white/60 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span className="text-white/60">No image available</span>
                  </div>
                </div>
              )}
            </div>

            {/* Product Details */}
            <div className="space-y-8">
              {/* Breadcrumb */}
              <nav className="flex items-center space-x-2 text-sm text-white/70">
                <a href="/collections/all" className="hover:text-white transition-colors">Coffee</a>
                <span className="text-white">→</span>
                <span className="text-white">{product.title}</span>
              </nav>

              {/* Product Title and Vendor */}
              <div>
                <p className="text-[#db8027] mb-2 uppercase tracking-wider text-sm font-semibold">{product.vendor}</p>
                <h1 className="text-3xl lg:text-4xl font-bold leading-tight mb-4 text-white">{title}</h1>

                {/* Coffee characteristics */}
                <div className="flex flex-wrap gap-2 mb-6">
                  <span className="bg-white/20 text-white px-3 py-1 rounded-full text-sm backdrop-blur-sm">
                    Premium Roast
                  </span>
                  <span className="bg-white/20 text-white px-3 py-1 rounded-full text-sm backdrop-blur-sm">
                    Single Origin
                  </span>
                  <span className="bg-white/20 text-white px-3 py-1 rounded-full text-sm backdrop-blur-sm">
                    Adventure Ready
                  </span>
                </div>
              </div>

              {/* Price */}
              <div className="text-2xl lg:text-3xl font-bold text-[#db8027]">
                <ProductPrice
                  price={selectedVariant?.price}
                  compareAtPrice={selectedVariant?.compareAtPrice}
                />
              </div>

              {/* Product Form */}
              <div className="bg-white/90 backdrop-blur-sm rounded-xl p-6 border border-white/30 shadow-lg">
                <ProductForm
                  productOptions={productOptions}
                  selectedVariant={selectedVariant}
                  product={product}
                />
              </div>

              {/* Quick Features */}
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-[#db8027] rounded-full flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                  </div>
                  <div>
                    <p className="font-semibold text-white">Expert Roasted</p>
                    <p className="text-sm text-white/70">Small batch perfection</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-[#db8027] rounded-full flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <p className="font-semibold text-white">Ethically Sourced</p>
                    <p className="text-sm text-white/70">Direct trade partners</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Product Description Section */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="bg-white rounded-2xl shadow-xl p-8 lg:p-12">
          <h2 className="text-2xl lg:text-3xl font-bold text-[#3a5c5c] mb-6 text-center">About This Coffee</h2>
          <div className="w-16 h-1 bg-[#db8027] mx-auto mb-8"></div>

          <div
            className="text-gray-700 leading-relaxed prose prose-lg max-w-none text-center"
            dangerouslySetInnerHTML={{__html: descriptionHtml}}
          />
        </div>
      </div>

      {/* Adventure Promise Section */}
      <div className="py-20 bg-gradient-to-r from-[#3a5c5c] to-[#2d4747]">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">Our Adventure Promise</h2>
            <div className="w-20 h-1 bg-[#db8027] mx-auto mb-6"></div>
            <p className="adventure-promise-text text-white/80 text-lg max-w-2xl mx-auto px-4 text-center">
              Every cup is crafted to fuel your next adventure, sourced with care and roasted to perfection.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 mb-12">
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 text-center border border-white/20 hover:bg-white/15 transition-all duration-300">
              <div className="w-16 h-16 bg-[#db8027] rounded-full flex items-center justify-center mb-6 mx-auto">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white mb-3">Ethically Sourced</h3>
              <p className="text-white/70">Direct partnerships with mountain farmers in Nicaragua, ensuring fair trade and sustainable practices.</p>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 text-center border border-white/20 hover:bg-white/15 transition-all duration-300">
              <div className="w-16 h-16 bg-[#db8027] rounded-full flex items-center justify-center mb-6 mx-auto">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white mb-3">Expert Roasted</h3>
              <p className="text-white/70">Small-batch roasting by master craftsmen, bringing out the unique flavors of each origin.</p>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 text-center border border-white/20 hover:bg-white/15 transition-all duration-300">
              <div className="w-16 h-16 bg-[#db8027] rounded-full flex items-center justify-center mb-6 mx-auto">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white mb-3">Adventure Ready</h3>
              <p className="text-white/70">Perfectly crafted to fuel any adventure, from mountain peaks to morning commutes.</p>
            </div>
          </div>

          <div className="text-center">
            <a
              href="/collections/all"
              className="inline-flex items-center px-8 py-4 bg-[#db8027] hover:bg-[#c4721f] text-white rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
            >
              Explore All Coffee
              <svg className="ml-2 w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </a>
          </div>
        </div>
      </div>

      <Analytics.ProductView
        data={{
          products: [
            {
              id: product.id,
              title: product.title,
              price: selectedVariant?.price.amount || '0',
              vendor: product.vendor,
              variantId: selectedVariant?.id || '',
              variantTitle: selectedVariant?.title || '',
              quantity: 1,
            },
          ],
        }}
      />
    </div>
  );
}

const PRODUCT_VARIANT_FRAGMENT = `#graphql
  fragment ProductVariant on ProductVariant {
    availableForSale
    compareAtPrice {
      amount
      currencyCode
    }
    id
    image {
      __typename
      id
      url
      altText
      width
      height
    }
    price {
      amount
      currencyCode
    }
    product {
      title
      handle
    }
    selectedOptions {
      name
      value
    }
    sku
    title
    unitPrice {
      amount
      currencyCode
    }
  }
` as const;

const PRODUCT_FRAGMENT = `#graphql
  fragment Product on Product {
    id
    title
    vendor
    handle
    descriptionHtml
    description
    encodedVariantExistence
    encodedVariantAvailability
    requiresSellingPlan
    images(first: 10) {
      nodes {
        id
        url
        altText
        width
        height
      }
    }
    sellingPlanGroups(first: 10) {
      nodes {
        name
        options {
          name
          values
        }
        sellingPlans(first: 10) {
          nodes {
            id
            name
            description
            options {
              name
              value
            }
            priceAdjustments {
              adjustmentValue {
                ... on SellingPlanFixedAmountPriceAdjustment {
                  adjustmentAmount {
                    amount
                    currencyCode
                  }
                }
                ... on SellingPlanFixedPriceAdjustment {
                  price {
                    amount
                    currencyCode
                  }
                }
                ... on SellingPlanPercentagePriceAdjustment {
                  adjustmentPercentage
                }
              }
              orderCount
            }
            recurringDeliveries
            checkoutCharge {
              type
              value {
                ... on SellingPlanCheckoutChargePercentageValue {
                  percentage
                }
                ... on MoneyV2 {
                  amount
                  currencyCode
                }
              }
            }
          }
        }
      }
    }
    options {
      name
      optionValues {
        name
        firstSelectableVariant {
          ...ProductVariant
        }
        swatch {
          color
          image {
            previewImage {
              url
            }
          }
        }
      }
    }
    selectedOrFirstAvailableVariant(selectedOptions: $selectedOptions, ignoreUnknownOptions: true, caseInsensitiveMatch: true) {
      ...ProductVariant
    }
    adjacentVariants (selectedOptions: $selectedOptions) {
      ...ProductVariant
    }
    seo {
      description
      title
    }
  }
  ${PRODUCT_VARIANT_FRAGMENT}
` as const;

const PRODUCT_QUERY = `#graphql
  query Product(
    $country: CountryCode
    $handle: String!
    $language: LanguageCode
    $selectedOptions: [SelectedOptionInput!]!
  ) @inContext(country: $country, language: $language) {
    product(handle: $handle) {
      ...Product
    }
  }
  ${PRODUCT_FRAGMENT}
` as const;
