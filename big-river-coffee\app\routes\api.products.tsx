import { type LoaderFunctionArgs, json } from '@shopify/remix-oxygen';

const CATALOG_QUERY_MINIMAL = `#graphql
  query CatalogMinimal(
    $country: CountryCode
    $language: LanguageCode
    $first: Int
    $after: String
    $query: String
  ) @inContext(country: $country, language: $language) {
    products(first: $first, after: $after, query: $query) {
      nodes {
        id
        handle
        title
        publishedAt
        availableForSale
        featuredImage {
          id
          altText
          url
          width
          height
        }
        priceRange {
          minVariantPrice {
            amount
            currencyCode
          }
        }
        compareAtPriceRange {
          minVariantPrice {
            amount
            currencyCode
          }
        }
        variants(first: 1) {
          nodes {
            id
            availableForSale
            selectedOptions {
              name
              value
            }
            price {
              amount
              currencyCode
            }
            compareAtPrice {
              amount
              currencyCode
            }
          }
        }
      }
      pageInfo {
        hasPreviousPage
        hasNextPage
        startCursor
        endCursor
      }
    }
  }
` as const;

/**
 * API route for progressive product loading
 * Optimized for fast response times with minimal data
 */
export async function loader({ request, context }: LoaderFunctionArgs) {
  const { storefront } = context;
  const url = new URL(request.url);
  
  // Get query parameters
  const cursor = url.searchParams.get('cursor');
  const query = url.searchParams.get('query') || '';
  const pageBy = parseInt(url.searchParams.get('pageBy') || '8');

  // Validate parameters
  if (!cursor) {
    return json({ error: 'Cursor is required' }, { status: 400 });
  }

  try {
    // Query products with caching
    const result = await storefront.query(CATALOG_QUERY_MINIMAL, {
      variables: {
        first: pageBy,
        after: cursor,
        query: query,
      },
      cache: storefront.CacheLong(), // Cache for 1 hour
    });

    // Return products data
    return json({
      products: result.products,
      success: true,
    }, {
      headers: {
        'Cache-Control': 'public, max-age=3600, stale-while-revalidate=86400',
      },
    });

  } catch (error) {
    console.error('Error loading products:', error);
    return json({
      error: 'Failed to load products',
      success: false,
    }, { status: 500 });
  }
}

// Collection-specific query for better performance
const COLLECTION_QUERY_MINIMAL = `#graphql
  query CollectionMinimal(
    $country: CountryCode
    $language: LanguageCode
    $handle: String!
    $first: Int
    $after: String
  ) @inContext(country: $country, language: $language) {
    collection(handle: $handle) {
      products(first: $first, after: $after) {
        nodes {
          id
          handle
          title
          publishedAt
          availableForSale
          featuredImage {
            id
            altText
            url
            width
            height
          }
          priceRange {
            minVariantPrice {
              amount
              currencyCode
            }
          }
          compareAtPriceRange {
            minVariantPrice {
              amount
              currencyCode
            }
          }
          variants(first: 1) {
            nodes {
              id
              availableForSale
              selectedOptions {
                name
                value
              }
              price {
                amount
                currencyCode
              }
              compareAtPrice {
                amount
                currencyCode
              }
            }
          }
        }
        pageInfo {
          hasPreviousPage
          hasNextPage
          startCursor
          endCursor
        }
      }
    }
  }
` as const;

/**
 * Alternative loader for collection-based product loading
 */
export async function collectionLoader({ request, context }: LoaderFunctionArgs) {
  const { storefront } = context;
  const url = new URL(request.url);
  
  const cursor = url.searchParams.get('cursor');
  const handle = url.searchParams.get('handle');
  const pageBy = parseInt(url.searchParams.get('pageBy') || '8');

  if (!cursor || !handle) {
    return json({ error: 'Cursor and handle are required' }, { status: 400 });
  }

  try {
    const result = await storefront.query(COLLECTION_QUERY_MINIMAL, {
      variables: {
        handle,
        first: pageBy,
        after: cursor,
      },
      cache: storefront.CacheLong(),
    });

    return json({
      products: result.collection?.products,
      success: true,
    }, {
      headers: {
        'Cache-Control': 'public, max-age=3600, stale-while-revalidate=86400',
      },
    });

  } catch (error) {
    console.error('Error loading collection products:', error);
    return json({
      error: 'Failed to load collection products',
      success: false,
    }, { status: 500 });
  }
}
