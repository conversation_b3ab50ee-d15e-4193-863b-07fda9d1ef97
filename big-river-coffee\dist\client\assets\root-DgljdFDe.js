const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/GoogleAnalytics-D6C8e2S5.js","assets/index-BgHXxrvA.js","assets/chunk-D4RADZKF-DNKU_WG6.js","assets/jsx-runtime-D5QEUsP9.js","assets/with-props-ZU_SrDpf.js","assets/Aside-HLZLr7KT.js","assets/CartMain-CuXLs3-6.js","assets/variants-J2GcD6hP.js","assets/ProductPrice-C6YWsE4Q.js","assets/Money-BAfw1cBI.js","assets/Image-BhFgnyoc.js","assets/search-DOeYwaXi.js","assets/GTMLoader-CGoYNIoT.js"])))=>i.map(i=>d[i]);
import{J as R,G as H,$ as F,s as D,_ as P}from"./index-BgHXxrvA.js";import{w as B,a as z}from"./with-props-ZU_SrDpf.js";import{j as e}from"./jsx-runtime-D5QEUsP9.js";import{N as h,r as d,E as y,G as $,d as T,c as V,L as x,b as W,i as E,H as Y,M as q,I as G,S as K,J,O as Q,h as Z,K as X}from"./chunk-D4RADZKF-DNKU_WG6.js";import{u as g,A as w}from"./Aside-HLZLr7KT.js";import{C as ee}from"./CartMain-CuXLs3-6.js";import{g as te,u as b}from"./search-DOeYwaXi.js";import{I as k}from"./Image-BhFgnyoc.js";import{M as se}from"./Money-BAfw1cBI.js";const re="/assets/favicon-DZkC1E9c.svg",ne="/assets/reset-BKioPaen.css",ie="/assets/app-evTZ8hrK.css",oe="/assets/homepage-7uZSHaEk.css",ae="/assets/tailwind-DyiqMwTb.css";function le({footer:t,header:s,publicStoreDomain:n}){const r=new Date().getFullYear();return e.jsxs("footer",{className:"text-white mt-auto",style:{backgroundColor:"#3A5C5C"},children:[e.jsx("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-12",children:[e.jsxs("div",{className:"col-span-1 md:col-span-2",children:[e.jsx(h,{to:"/",className:"inline-block mb-6",children:e.jsx("img",{src:"/Logo_Official.svg",alt:"Big River Coffee",className:"h-20 w-auto"})}),e.jsx("p",{className:"text-white mb-12 text-base leading-relaxed max-w-md",children:"Premium coffee for adventurers. Ethically sourced from mountain regions, expertly roasted for those who live life to the fullest."}),e.jsxs("div",{className:"flex space-x-4 mt-4",children:[e.jsx("a",{href:"https://www.instagram.com/bigriver.coffee/",target:"_blank",rel:"noopener noreferrer",className:"w-10 h-10 bg-army-600 rounded-lg flex items-center justify-center text-white hover:text-white hover:bg-army-500 transition-all duration-200","aria-label":"Follow us on Instagram",children:e.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"})})}),e.jsx("a",{href:"https://www.facebook.com/bigriverco/",target:"_blank",rel:"noopener noreferrer",className:"w-10 h-10 bg-army-600 rounded-lg flex items-center justify-center text-white hover:text-white hover:bg-army-500 transition-all duration-200","aria-label":"Follow us on Facebook",children:e.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})})}),e.jsx("a",{href:"https://www.tiktok.com/@bigriver.coffee",target:"_blank",rel:"noopener noreferrer",className:"w-10 h-10 bg-army-600 rounded-lg flex items-center justify-center text-white hover:text-white hover:bg-army-500 transition-all duration-200","aria-label":"Follow us on TikTok",children:e.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M19.59 6.69a4.83 4.83 0 01-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 01-5.2 1.74 2.89 2.89 0 012.31-4.64 2.93 2.93 0 01.88.13V9.4a6.84 6.84 0 00-.88-.05A6.33 6.33 0 005 20.1a6.34 6.34 0 0010.86-4.43v-7a8.16 8.16 0 004.77 1.52v-3.4a4.85 4.85 0 01-1-.1z"})})})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-white text-lg font-semibold mb-6",children:"Shop"}),e.jsxs("ul",{className:"space-y-3",children:[e.jsx("li",{children:e.jsx(h,{to:"/collections/all",className:"text-white hover:text-orange-300 transition-colors duration-200 text-sm",children:"Coffee"})}),e.jsx("li",{children:e.jsx(h,{to:"/collections/all",className:"text-white hover:text-orange-300 transition-colors duration-200 text-sm",children:"K-Cups"})}),e.jsx("li",{children:e.jsx(h,{to:"/collections/all",className:"text-white hover:text-orange-300 transition-colors duration-200 text-sm",children:"Subscriptions"})}),e.jsx("li",{children:e.jsx(h,{to:"/our-story",className:"text-white hover:text-orange-300 transition-colors duration-200 text-sm",children:"Our Story"})})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-white text-lg font-semibold mb-6",children:"Help"}),e.jsxs("ul",{className:"space-y-3",children:[e.jsx("li",{children:e.jsx(h,{to:"/policies/shipping-policy",className:"text-white hover:text-orange-300 transition-colors duration-200 text-sm",children:"Shipping Info"})}),e.jsx("li",{children:e.jsx(h,{to:"/policies/refund-policy",className:"text-white hover:text-orange-300 transition-colors duration-200 text-sm",children:"Returns"})}),e.jsx("li",{children:e.jsx(h,{to:"/account",className:"text-white hover:text-orange-300 transition-colors duration-200 text-sm",children:"My Account"})})]})]})]})}),e.jsx("div",{className:"border-t border-white/20",children:e.jsx("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:e.jsxs("div",{className:"md:flex md:items-center md:justify-between",children:[e.jsxs("div",{className:"text-sm text-white",children:["© ",r," Big River Coffee. All rights reserved."]}),e.jsx("div",{className:"mt-4 md:mt-0",children:e.jsx(d.Suspense,{children:e.jsx(y,{resolve:t,children:i=>{var l;return(i==null?void 0:i.menu)&&((l=s.shop.primaryDomain)==null?void 0:l.url)&&e.jsx(ce,{menu:i.menu,primaryDomainUrl:s.shop.primaryDomain.url,publicStoreDomain:n})}})})})]})})})]})}function ce({menu:t,primaryDomainUrl:s,publicStoreDomain:n}){return e.jsx("nav",{role:"navigation",children:e.jsx("ul",{className:"flex flex-wrap gap-6",children:(t||ue).items.map(r=>{if(!r.url)return null;const i=r.url.includes("myshopify.com")||r.url.includes(n)||r.url.includes(s)?new URL(r.url).pathname:r.url,l=!i.startsWith("/");return e.jsx("li",{children:l?e.jsx("a",{href:i,rel:"noopener noreferrer",target:"_blank",className:"text-sm text-white hover:text-orange-300 transition-colors duration-200",children:r.title}):e.jsx(h,{end:!0,prefetch:"intent",to:i,className:"text-sm text-white hover:text-orange-300 transition-colors duration-200",children:r.title})},r.id)})})})}const ue={items:[{id:"gid://shopify/MenuItem/461633060920",resourceId:"gid://shopify/ShopPolicy/23358046264",tags:[],title:"Privacy Policy",type:"SHOP_POLICY",url:"/policies/privacy-policy",items:[]},{id:"gid://shopify/MenuItem/461633093688",resourceId:"gid://shopify/ShopPolicy/23358013496",tags:[],title:"Refund Policy",type:"SHOP_POLICY",url:"/policies/refund-policy",items:[]},{id:"gid://shopify/MenuItem/461633126456",resourceId:"gid://shopify/ShopPolicy/23358111800",tags:[],title:"Shipping Policy",type:"SHOP_POLICY",url:"/policies/shipping-policy",items:[]},{id:"gid://shopify/MenuItem/461633159224",resourceId:"gid://shopify/ShopPolicy/23358079032",tags:[],title:"Terms of Service",type:"SHOP_POLICY",url:"/policies/terms-of-service",items:[]}]};function de({header:t,isLoggedIn:s,cart:n,publicStoreDomain:r}){const{shop:i,menu:l}=t,[o,a]=d.useState(!1),[u,c]=d.useState(!1),[m,p]=d.useState(0);return d.useEffect(()=>{const j=()=>{const v=window.scrollY;a(v>50),v>150?c(!0):c(!1),p(v)};return window.addEventListener("scroll",j),()=>window.removeEventListener("scroll",j)},[m]),e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"fixed top-0 left-0 right-0 z-50 text-white py-1.5 px-4",style:{backgroundColor:"#7895a4"},children:e.jsx("div",{className:"max-w-7xl mx-auto text-center",children:e.jsxs("p",{className:"text-sm font-semibold",children:["🔥 30% OFF EVERYTHING WITH CODE"," ",e.jsx("span",{className:"bg-white px-2 py-1 rounded font-bold text-xs",style:{color:"#7895a4"},children:"SUMMER30"})," ","🔥"]})})}),e.jsx("header",{className:`fixed left-0 right-0 z-40 transition-all duration-300 ${u?"translate-y-0":"-translate-y-full"}`,style:{top:"32px",height:"calc(var(--header-height) - 32px)",backgroundColor:"#3A5C5C",background:"#3A5C5C"},children:e.jsx("div",{className:"max-w-7xl mx-auto relative px-4 sm:px-6 lg:px-8 h-full",children:e.jsxs("div",{className:"flex items-center justify-between h-full",children:[e.jsx("div",{className:"flex justify-start items-center h-full",children:e.jsx(h,{to:"/",prefetch:"intent",end:!0,className:"relative group flex items-center h-full",children:e.jsx("img",{src:"/headerlogo.svg",alt:"Big River Coffee",className:"transition-all duration-300 group-hover:scale-105 h-24 sm:h-32 lg:h-36 w-auto",width:"auto",height:"140"})})}),e.jsx("div",{className:"hidden md:flex items-center space-x-8",children:e.jsx(I,{menu:null,viewport:"desktop",primaryDomainUrl:t.shop.primaryDomain.url,publicStoreDomain:r})}),e.jsx("div",{className:"flex-1 flex justify-end",children:e.jsx(me,{isLoggedIn:s,cart:n})})]})})})]})}function I({menu:t,primaryDomainUrl:s,viewport:n,publicStoreDomain:r}){var l;const{close:i}=g();return n==="mobile"?(console.log("Mobile menu rendering:",{menuExists:!!t,fallbackItems:N.items.length,menuItems:((l=t==null?void 0:t.items)==null?void 0:l.length)||0}),e.jsxs("nav",{className:"header-menu-mobile",role:"navigation",children:[e.jsx(h,{end:!0,onClick:i,prefetch:"intent",to:"/",className:"header-menu-item",children:"Home"}),e.jsx(h,{to:"/rewards",className:"header-menu-item",onClick:i,children:"Rewards"}),N.items.map(o=>{if(!o.url)return null;let a=o.url;if(o.url.includes("myshopify.com")||o.url.includes(r)||o.url.includes(s))try{a=new URL(o.url).pathname}catch{a=o.url}return(a.includes("/products/big-river-k-cups")||o.title&&o.title.toLowerCase().includes("k-cup"))&&(a="/collections/all?section=kcups"),a.includes("#section-subscriptions")&&(a="/collections/all?section=subscriptions"),e.jsx(h,{end:!0,onClick:i,prefetch:"intent",to:a,className:"header-menu-item",children:o.title},o.id)})]})):e.jsx("nav",{className:"flex items-center space-x-8",role:"navigation",children:N.items.map(o=>{if(!o.url)return null;let a=o.url;if(o.url.includes("myshopify.com")||o.url.includes(r)||o.url.includes(s))try{a=new URL(o.url).pathname}catch{a=o.url}return(a.includes("/products/big-river-k-cups")||o.title&&o.title.toLowerCase().includes("k-cup"))&&(a="/collections/all?section=kcups"),a.includes("#section-subscriptions")&&(a="/collections/all?section=subscriptions"),e.jsx(h,{end:!0,prefetch:"intent",to:a,className:({isActive:u})=>`text-white hover:text-orange-300 transition-colors duration-200 font-medium ${u?"text-orange-300":"text-white"}`,children:o.title},o.id)})})}function me({isLoggedIn:t,cart:s}){return e.jsxs("nav",{className:"flex items-center space-x-4",role:"navigation",children:[e.jsxs("div",{className:"hidden md:flex items-center space-x-4",children:[e.jsx(h,{prefetch:"intent",to:"/account",className:"text-white hover:text-orange-300 transition-colors duration-200 font-medium",children:e.jsx(d.Suspense,{fallback:"Sign in",children:e.jsx(y,{resolve:t,errorElement:"Sign in",children:n=>n?"Account":"Sign in"})})}),e.jsx(fe,{})]}),e.jsx(pe,{cart:s}),e.jsx("div",{className:"md:hidden",children:e.jsx(he,{})})]})}function he(){const{open:t}=g();return e.jsx("button",{className:"text-white hover:text-orange-300 transition-colors duration-200 p-2",onClick:()=>t("mobile"),"aria-label":"Open mobile menu",style:{color:"white"},children:e.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})}function fe(){const{open:t}=g();return e.jsx("button",{className:"text-white hover:text-orange-300 transition-colors duration-200 p-2",onClick:()=>t("search"),"aria-label":"Open search",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})})}function A({count:t}){const{open:s}=g(),{publish:n,shop:r,cart:i,prevCart:l}=H();return e.jsxs("button",{onClick:o=>{o.preventDefault(),s("cart"),n("cart_viewed",{cart:i,prevCart:l,shop:r,url:window.location.href||""})},className:"relative text-white hover:text-orange-300 transition-colors duration-200 p-2","aria-label":`Cart with ${t||0} items`,children:[e.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m0 0h8"})}),t!==null&&t>0&&e.jsx("span",{className:"absolute -top-1 -right-1 bg-amber-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold",children:t})]})}function pe({cart:t}){return e.jsx(d.Suspense,{fallback:e.jsx(A,{count:null}),children:e.jsx(y,{resolve:t,children:e.jsx(xe,{})})})}function xe(){const t=$(),s=R(t);return e.jsx(A,{count:(s==null?void 0:s.totalQuantity)??0})}const N={items:[{id:"gid://shopify/MenuItem/461609500728",resourceId:null,tags:[],title:"Coffee",type:"HTTP",url:"/collections/all",items:[]},{id:"gid://shopify/MenuItem/461609500729",resourceId:null,tags:[],title:"K-Cups",type:"HTTP",url:"/collections/all?section=kcups",items:[]},{id:"gid://shopify/MenuItem/461609500731",resourceId:null,tags:[],title:"Subscriptions",type:"HTTP",url:"/collections/all#section-subscriptions",items:[]},{id:"gid://shopify/MenuItem/461609500730",resourceId:null,tags:[],title:"Brew",type:"HTTP",url:"/pages/brew",items:[]},{id:"gid://shopify/MenuItem/461609599032",resourceId:"gid://shopify/Page/92591030328",tags:[],title:"Our Story",type:"PAGE",url:"/our-story",items:[]},{id:"gid://shopify/MenuItem/461609566264",resourceId:null,tags:[],title:"Contact",type:"HTTP",url:"/contact",items:[]},{id:"gid://shopify/MenuItem/461609566266",resourceId:null,tags:[],title:"Rewards",type:"HTTP",url:"/rewards",items:[]},{id:"gid://shopify/MenuItem/461609566265",resourceId:null,tags:[],title:"Affiliate",type:"HTTP",url:"/affiliate",items:[]}]},_="/search";function ge({children:t,className:s="predictive-search-form",...n}){const r=T({key:"search"}),i=d.useRef(null),l=V(),o=g();function a(m){var p;m.preventDefault(),m.stopPropagation(),(p=i==null?void 0:i.current)!=null&&p.value&&i.current.blur()}function u(){var p;const m=(p=i==null?void 0:i.current)==null?void 0:p.value;l(_+(m?`?q=${m}`:"")),o.close()}function c(m){r.submit({q:m.target.value||"",limit:5,predictive:!0},{method:"GET",action:_})}return d.useEffect(()=>{var m;(m=i==null?void 0:i.current)==null||m.setAttribute("type","search")},[]),typeof t!="function"?null:e.jsx(r.Form,{...n,className:s,onSubmit:a,children:t({inputRef:i,fetcher:r,fetchResults:c,goToSearch:u})})}function f({children:t}){const s=g(),{term:n,inputRef:r,fetcher:i,total:l,items:o}=_e();function a(){r.current&&(r.current.blur(),r.current.value="")}function u(){a(),s.close()}return t({items:o,closeSearch:u,inputRef:r,state:i.state,term:n,total:l})}f.Articles=je;f.Collections=ve;f.Pages=ye;f.Products=we;f.Queries=be;f.Empty=Ne;function je({term:t,articles:s,closeSearch:n}){return s.length?e.jsxs("div",{className:"predictive-search-result",children:[e.jsx("h5",{children:"Articles"}),e.jsx("ul",{children:s.map(r=>{var l;const i=b({baseUrl:`/blogs/${r.blog.handle}/${r.handle}`,trackingParams:r.trackingParameters,term:t.current??""});return e.jsx("li",{className:"predictive-search-result-item",children:e.jsxs(x,{onClick:n,to:i,children:[((l=r.image)==null?void 0:l.url)&&e.jsx(k,{alt:r.image.altText??"",src:r.image.url,width:50,height:50}),e.jsx("div",{children:e.jsx("span",{children:r.title})})]})},r.id)})})]},"articles"):null}function ve({term:t,collections:s,closeSearch:n}){return s.length?e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-semibold text-army-700 mb-3 uppercase tracking-wide",children:"Collections"}),e.jsx("div",{className:"space-y-2",children:s.map(r=>{var l;const i=b({baseUrl:`/collections/${r.handle}`,trackingParams:r.trackingParameters,term:t.current});return e.jsx(x,{onClick:n,to:i,className:"block p-3 bg-white rounded-lg border border-army-100 hover:border-army-300 hover:bg-army-50 transition-colors duration-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[((l=r.image)==null?void 0:l.url)&&e.jsx(k,{alt:r.image.altText??"",src:r.image.url,width:32,height:32,className:"w-8 h-8 object-cover rounded"}),e.jsx("span",{className:"text-gray-900 font-medium",children:r.title})]}),e.jsx("svg",{className:"w-4 h-4 text-army-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})},r.id)})})]},"collections"):null}function ye({term:t,pages:s,closeSearch:n}){return s.length?e.jsxs("div",{className:"predictive-search-result",children:[e.jsx("h5",{children:"Pages"}),e.jsx("ul",{children:s.map(r=>{const i=b({baseUrl:`/pages/${r.handle}`,trackingParams:r.trackingParameters,term:t.current});return e.jsx("li",{className:"predictive-search-result-item",children:e.jsx(x,{onClick:n,to:i,children:e.jsx("div",{children:e.jsx("span",{children:r.title})})})},r.id)})})]},"pages"):null}function we({term:t,products:s,closeSearch:n}){return s.length?e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-semibold text-army-700 mb-3 uppercase tracking-wide",children:"Products"}),e.jsx("div",{className:"space-y-3",children:s.map(r=>{var a,u;const i=b({baseUrl:`/products/${r.handle}`,trackingParams:r.trackingParameters,term:t.current}),l=(a=r==null?void 0:r.selectedOrFirstAvailableVariant)==null?void 0:a.price,o=(u=r==null?void 0:r.selectedOrFirstAvailableVariant)==null?void 0:u.image;return e.jsx(x,{to:i,onClick:n,className:"block p-3 bg-white rounded-lg border border-army-100 hover:border-army-300 hover:bg-army-50 transition-colors duration-200",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[o&&e.jsx("div",{className:"flex-shrink-0",children:e.jsx(k,{alt:o.altText??"",src:o.url,width:48,height:48,className:"w-12 h-12 object-cover rounded-lg"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900 truncate",children:r.title}),l&&e.jsx("p",{className:"text-sm text-army-600 font-semibold",children:e.jsx(se,{data:l})})]}),e.jsx("svg",{className:"w-4 h-4 text-army-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})},r.id)})})]},"products"):null}function be({queries:t,queriesDatalistId:s}){return t.length?e.jsx("datalist",{id:s,children:t.map(n=>n?e.jsx("option",{value:n.text},n.text):null)}):null}function Ne({term:t}){return t.current?e.jsx("div",{className:"py-12 text-center",children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("div",{className:"w-16 h-16 bg-army-100 rounded-full flex items-center justify-center mb-4",children:e.jsx("svg",{className:"w-8 h-8 text-army-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No results found"}),e.jsxs("p",{className:"text-gray-600 mb-4",children:["No results found for ",e.jsx("q",{className:"font-medium text-army-600",children:t.current})]}),e.jsx("p",{className:"text-sm text-gray-500",children:"Try searching for coffee, gear, or check your spelling"})]})}):null}function _e(){var l,o;const t=T({key:"search"}),s=d.useRef(""),n=d.useRef(null);(t==null?void 0:t.state)==="loading"&&(s.current=String(((l=t.formData)==null?void 0:l.get("q"))||"")),d.useEffect(()=>{n.current||(n.current=document.querySelector('input[type="search"]'))},[]);const{items:r,total:i}=((o=t==null?void 0:t.data)==null?void 0:o.result)??te();return{items:r,total:i,inputRef:n,term:s,fetcher:t}}const S="bigriver_utm_params",C="bigriver_utm_expiry",ke=24;function L(t){const s={};return["utm_source","utm_medium","utm_campaign","utm_content","utm_term","utm_id"].forEach(r=>{const i=t.get(r);i&&(s[r]=i)}),s}function Se(t){if(!(typeof window>"u"))try{if(Object.keys(t).length>0){const s=Date.now()+ke*60*60*1e3;sessionStorage.setItem(S,JSON.stringify(t)),sessionStorage.setItem(C,s.toString()),console.log("UTM parameters stored:",t)}}catch(s){console.warn("Failed to store UTM parameters:",s)}}function Ce(){if(typeof window>"u")return null;try{const t=sessionStorage.getItem(S),s=sessionStorage.getItem(C);return!t||!s?null:Date.now()>parseInt(s)?(Me(),null):JSON.parse(t)}catch(t){return console.warn("Failed to retrieve UTM parameters:",t),null}}function Me(){if(!(typeof window>"u"))try{sessionStorage.removeItem(S),sessionStorage.removeItem(C)}catch(t){console.warn("Failed to clear UTM parameters:",t)}}function Pe(t){if(t){const s=L(t);if(Object.keys(s).length>0)return Se(s),s}return Ce()||{}}function Te(t,s){const n=new URL(t,window.location.origin);return Object.entries(s).forEach(([r,i])=>{i&&n.searchParams.set(r,i)}),n.toString()}function et(t){return{campaign_source:t.utm_source,campaign_medium:t.utm_medium,campaign_name:t.utm_campaign,campaign_content:t.utm_content,campaign_term:t.utm_term,campaign_id:t.utm_id}}function Ee({to:t,utmParams:s,children:n,className:r,onClick:i,external:l=!1}){if(l){let a=t;return typeof window<"u"&&(a=Te(t,s)),e.jsx("a",{href:a,className:r,onClick:i,target:"_blank",rel:"noopener noreferrer",children:n})}let o=t;if(typeof window<"u"){const a=new URL(t,window.location.origin);Object.entries(s).forEach(([u,c])=>{c&&a.searchParams.set(u,c)}),o=a.pathname+a.search}return e.jsx(x,{to:o,className:r,onClick:i,children:n})}const M={PopupCTA:({children:t,className:s,to:n="/collections/all"})=>e.jsx(Ee,{to:n,utmParams:{utm_source:"website",utm_medium:"popup",utm_campaign:"promo_popup",utm_content:"shop_coffee_button"},className:s,children:t})};function Ie({isOpen:t,onClose:s}){const[n,r]=d.useState(!1),[i,l]=d.useState(!1);return d.useEffect(()=>{l(!0);const o=()=>{r(window.innerWidth<768)};return o(),window.addEventListener("resize",o),()=>window.removeEventListener("resize",o)},[]),d.useEffect(()=>(t?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[t]),!t||!i?null:e.jsxs("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[e.jsx("div",{className:"absolute inset-0 backdrop-blur-sm",onClick:s}),e.jsxs("div",{className:"relative z-10 max-w-4xl mx-4 max-h-[90vh] overflow-hidden",children:[e.jsx("button",{onClick:s,className:"absolute top-4 right-4 z-20 w-10 h-10 bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full flex items-center justify-center shadow-lg transition-all duration-200 hover:scale-110","aria-label":"Close popup",children:e.jsx("svg",{className:"w-6 h-6 text-gray-800",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),e.jsxs("div",{className:"relative bg-white rounded-lg shadow-2xl overflow-hidden border-4 border-white",children:[!n&&e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:"/desktoppopup.webp",alt:"Special Offer",className:"w-full h-auto max-h-[80vh] object-contain"}),e.jsx("div",{className:"absolute bottom-6 left-6",children:e.jsxs(M.PopupCTA,{className:"inline-flex items-center px-8 py-4 bg-army-600 hover:bg-army-700 font-semibold rounded-lg shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl",children:[e.jsx("span",{className:"text-white",children:"Shop Coffee"}),e.jsx("svg",{className:"w-5 h-5 ml-2 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M14 5l7 7m0 0l-7 7m7-7H3"})})]})})]}),n&&e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:"/mobilepopup.webp",alt:"Special Offer",className:"w-full h-auto max-h-[80vh] object-contain"}),e.jsx("div",{className:"absolute bottom-6 left-1/2 transform -translate-x-1/2",children:e.jsxs(M.PopupCTA,{className:"inline-flex items-center px-6 py-3 bg-army-600 hover:bg-army-700 font-semibold rounded-lg shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl text-sm",children:[e.jsx("span",{className:"text-white",children:"Shop Coffee"}),e.jsx("svg",{className:"w-4 h-4 ml-2 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M14 5l7 7m0 0l-7 7m7-7H3"})})]})})]})]})]})]})}function Ae(){const[t]=W(),[s,n]=d.useState({}),[r,i]=d.useState(!0);return d.useEffect(()=>{const l=L(t),o=Pe(t);n(o),i(!1),Object.keys(l).length>0&&console.log("UTM parameters captured from URL:",l),Object.keys(o).length>0&&console.log("Active UTM parameters:",o)},[t]),{utmParams:s,isLoading:r,hasUTM:Object.keys(s).length>0}}function Le(){const{utmParams:t,hasUTM:s}=Ae();return d.useEffect(()=>{s&&typeof window<"u"&&(window.gtag&&(window.gtag("event","traffic_source_identified",{event_category:"Traffic Attribution",event_label:`${t.utm_source} / ${t.utm_medium}`,traffic_source:t.utm_source,traffic_medium:t.utm_medium,traffic_campaign:t.utm_campaign,traffic_content:t.utm_content,traffic_term:t.utm_term,custom_parameter_1:t.utm_source,custom_parameter_2:t.utm_medium}),O(t.utm_source,t.utm_medium)&&(console.log("🎯 SOCIAL MEDIA TRAFFIC DETECTED:",{platform:t.utm_source,campaign:t.utm_campaign,content:t.utm_content,timestamp:new Date().toISOString(),url:window.location.href}),window.gtag("event","social_media_traffic",{event_category:"Social Media Attribution",event_label:t.utm_source,social_platform:t.utm_source,social_campaign:t.utm_campaign,social_content:t.utm_content})),U(t.utm_medium)&&(console.log("💰 PAID ADVERTISING TRAFFIC DETECTED:",{source:t.utm_source,medium:t.utm_medium,campaign:t.utm_campaign,term:t.utm_term,timestamp:new Date().toISOString()}),window.gtag("event","paid_advertising_traffic",{event_category:"Paid Advertising Attribution",event_label:`${t.utm_source} - ${t.utm_campaign}`,ad_source:t.utm_source,ad_medium:t.utm_medium,ad_campaign:t.utm_campaign,ad_term:t.utm_term}))),console.log("📊 TRAFFIC SOURCE ATTRIBUTION:",{source:t.utm_source,medium:t.utm_medium,campaign:t.utm_campaign,content:t.utm_content,term:t.utm_term,type:Oe(t.utm_source,t.utm_medium),timestamp:new Date().toISOString(),page:window.location.pathname}))},[s,t]),null}function O(t,s){if(!t&&!s)return!1;const n=["facebook","instagram","twitter","linkedin","tiktok","snapchat","pinterest","youtube","reddit"],r=["social","social-media","social_media"];return n.includes((t==null?void 0:t.toLowerCase())||"")||r.includes((s==null?void 0:s.toLowerCase())||"")}function U(t){return t?["cpc","ppc","paid","ads","advertising","paid-social","paid_social","display","banner"].includes(t.toLowerCase()):!1}function Oe(t,s){return O(t,s)?"Social Media":U(s)?"Paid Advertising":s==="email"?"Email Marketing":s==="referral"?"Referral":s==="organic"?"Organic Search":"Other"}function Ue(){return d.useEffect(()=>{const t=()=>{(window.innerWidth<=768?["/headerlogo.svg","/newhomepage/mobile_homeage_bg_sf.webp","/mobilepopup.webp"]:["/headerlogo.svg","/hpheronew.svg","/newhomepage/bg_video/bg_sf_new.webp","/brewedforwild.webp"]).forEach(c=>{const m=document.createElement("link");m.rel="preload",m.as="image",m.href=c,document.head.appendChild(m)})},s=()=>{const a=document.querySelectorAll("img:not([loading])"),u=window.innerWidth<=768;a.forEach((c,m)=>{const p=u?1:3,v=c.getBoundingClientRect().top<window.innerHeight;m<p&&v?c.setAttribute("loading","eager"):c.setAttribute("loading","lazy"),c.setAttribute("decoding","async")})},n=()=>{if("IntersectionObserver"in window){const a=new IntersectionObserver(u=>{u.forEach(c=>{if(c.isIntersecting){const m=c.target;m.dataset.src&&(m.src=m.dataset.src,m.removeAttribute("data-src"),a.unobserve(m))}})});document.querySelectorAll("img[data-src]").forEach(u=>{a.observe(u)})}},r=()=>{const a=document.querySelectorAll("video"),u=window.innerWidth<=768;a.forEach(c=>{u?(c.setAttribute("preload","none"),c.paused||c.pause()):c.hasAttribute("preload")||c.setAttribute("preload","metadata"),"loading"in HTMLVideoElement.prototype&&c.setAttribute("loading","lazy"),c.hasAttribute("data-optimized")||(new IntersectionObserver(p=>{p.forEach(j=>{j.isIntersecting?c.hasAttribute("autoplay")&&c.play().catch(()=>{}):c.pause()})},{threshold:.3,rootMargin:"50px"}).observe(c),c.setAttribute("data-optimized","true"))})},i=()=>{document.querySelectorAll("script[src]:not([defer]):not([async])").forEach(u=>{const c=u.getAttribute("src");c&&!c.includes("gtag")&&!c.includes("analytics")&&u.setAttribute("defer","")})},l=()=>{const a=document.createElement("style");a.textContent=`
        @font-face {
          font-display: swap;
        }
      `,document.head.appendChild(a)},o=()=>{if(!(window.innerWidth<=768))return;const u=document.createElement("style");u.textContent=`
        @media (max-width: 768px) {
          *, *::before, *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
          }

          /* Disable expensive CSS effects on mobile */
          .transition-all,
          .transition-opacity,
          .transition-transform {
            transition: none !important;
          }
        }
      `,document.head.appendChild(u),document.body.classList.add("mobile-device")};return t(),s(),n(),r(),i(),l(),o(),()=>{}},[]),null}function Re({children:t}){const s=E(),[n,r]=d.useState(!0);return d.useEffect(()=>{if(s.pathname){r(!1);const i=setTimeout(()=>r(!0),100);return()=>clearTimeout(i)}},[s.pathname]),e.jsx("div",{className:`transition-all duration-300 ${n?"opacity-100 translate-y-0":"opacity-0 translate-y-2"}`,style:{minHeight:"200px"},children:t})}function He({cart:t,children:s=null,footer:n,header:r,isLoggedIn:i,publicStoreDomain:l}){const[a,u]=d.useState(!1);d.useEffect(()=>{},[!1]);const c=()=>{u(!1),localStorage.setItem("bigriver-promo-popup-seen","true")};return e.jsxs(w.Provider,{children:[e.jsx(Fe,{cart:t}),e.jsx(De,{}),e.jsx(Be,{header:r,publicStoreDomain:l}),r&&e.jsx(de,{header:r,cart:t,isLoggedIn:i,publicStoreDomain:l}),e.jsx("main",{children:e.jsx(Re,{children:s})}),e.jsx(le,{footer:n,header:r,publicStoreDomain:l}),e.jsx(Le,{}),e.jsx(Ue,{}),e.jsx(Ie,{isOpen:a,onClose:c})]})}function Fe({cart:t}){return e.jsx(w,{type:"cart",heading:"Shopping Cart",children:e.jsx(d.Suspense,{fallback:e.jsxs("div",{className:"flex items-center justify-center py-8",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-army-600"}),e.jsx("span",{className:"ml-3 text-army-600 font-medium",children:"Loading cart..."})]}),children:e.jsx(y,{resolve:t,children:s=>e.jsx(ee,{cart:s,layout:"aside"})})})})}function De(){const t=d.useId();return e.jsx(w,{type:"search",heading:"SEARCH",children:e.jsxs("div",{className:"h-full flex flex-col space-y-6",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx(ge,{children:({fetchResults:s,goToSearch:n,inputRef:r})=>e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"relative",children:e.jsx("input",{name:"q",onChange:s,onFocus:s,placeholder:"Search for coffee, gear, or anything...",ref:r,type:"search",list:t,className:"w-full px-4 py-3 bg-white border border-army-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-army-500 focus:border-army-500 text-gray-900 placeholder-gray-500"})}),e.jsx("button",{onClick:n,className:"w-full bg-army-600 text-white py-3 px-4 rounded-lg hover:bg-army-700 transition-colors duration-200 font-medium",children:"Search All Results"})]})})}),e.jsx("div",{className:"flex-1 overflow-y-auto",children:e.jsx(f,{children:({items:s,total:n,term:r,state:i,closeSearch:l})=>{const{articles:o,collections:a,pages:u,products:c,queries:m}=s;return i==="loading"&&r.current?e.jsxs("div",{className:"flex items-center justify-center py-8",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-army-600"}),e.jsx("span",{className:"ml-3 text-army-600 font-medium",children:"Searching..."})]}):n?e.jsxs("div",{className:"space-y-6",children:[e.jsx(f.Queries,{queries:m,queriesDatalistId:t}),e.jsx(f.Products,{products:c,closeSearch:l,term:r}),e.jsx(f.Collections,{collections:a,closeSearch:l,term:r}),e.jsx(f.Pages,{pages:u,closeSearch:l,term:r}),e.jsx(f.Articles,{articles:o,closeSearch:l,term:r}),r.current&&n?e.jsx("div",{className:"pt-4 border-t border-army-200",children:e.jsxs(x,{onClick:l,to:`${_}?q=${r.current}`,className:"block w-full p-4 bg-army-600 text-white rounded-lg hover:bg-army-700 transition-colors duration-200 text-center font-medium",children:["View all ",n,' results for "',r.current,'"']})}):null]}):e.jsx(f.Empty,{term:r})}})})]})})}function Be({header:t,publicStoreDomain:s}){var n;return t.menu&&((n=t.shop.primaryDomain)==null?void 0:n.url)&&e.jsx(w,{type:"mobile",heading:"MENU",children:e.jsx(I,{menu:t.menu,viewport:"mobile",primaryDomainUrl:t.shop.primaryDomain.url,publicStoreDomain:s})})}const ze=d.lazy(()=>P(()=>import("./GoogleAnalytics-D6C8e2S5.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11])).then(t=>({default:t.GoogleAnalytics}))),$e=d.lazy(()=>P(()=>import("./GTMLoader-CGoYNIoT.js"),__vite__mapDeps([12,2])).then(t=>({default:t.GTMLoader}))),tt=({formMethod:t,currentUrl:s,nextUrl:n})=>!!(t&&t!=="GET"||s.toString()===n.toString());function st(){return[{rel:"preconnect",href:"https://cdn.shopify.com"},{rel:"preconnect",href:"https://shop.app"},{rel:"preconnect",href:"https://www.googletagmanager.com"},{rel:"preconnect",href:"https://www.google-analytics.com"},{rel:"preconnect",href:"https://fonts.googleapis.com"},{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"},{rel:"stylesheet",href:"https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Oswald:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700;800&display=swap"},{rel:"preload",href:"/headerlogo.svg",as:"image",type:"image/svg+xml"},{rel:"preload",href:"/hpheronew.svg",as:"image",type:"image/svg+xml"},{rel:"icon",type:"image/svg+xml",href:re}]}function rt({children:t}){const s=F(),n=Y("root"),r=E(),i=r.pathname==="/"||r.pathname==="";return e.jsxs("html",{lang:"en",children:[e.jsxs("head",{children:[e.jsx("meta",{charSet:"utf-8"}),e.jsx("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),e.jsx("meta",{name:"theme-color",content:"#4a5d23"}),e.jsx("meta",{name:"format-detection",content:"telephone=no"}),e.jsx("meta",{httpEquiv:"x-dns-prefetch-control",content:"on"}),e.jsx("link",{rel:"dns-prefetch",href:"//cdn.shopify.com"}),e.jsx("link",{rel:"dns-prefetch",href:"//www.googletagmanager.com"}),e.jsx("link",{rel:"dns-prefetch",href:"//www.google-analytics.com"}),e.jsx("link",{rel:"icon",type:"image/x-icon",href:"/Logo_Official.ico?v=1"}),e.jsx("link",{rel:"shortcut icon",type:"image/x-icon",href:"/Logo_Official.ico?v=1"}),e.jsx("link",{rel:"apple-touch-icon",href:"/Logo_Official.ico?v=1"}),e.jsx("link",{rel:"stylesheet",href:ne}),e.jsx("link",{rel:"stylesheet",href:ie}),e.jsx("link",{rel:"stylesheet",href:oe}),e.jsx("link",{rel:"stylesheet",href:ae}),e.jsx(q,{}),e.jsx(G,{})]}),e.jsxs("body",{className:i?"homepage":"",children:[n?e.jsxs(D.Provider,{cart:n.cart,shop:n.shop,consent:n.consent,children:[e.jsxs(d.Suspense,{fallback:null,children:[e.jsx($e,{}),e.jsx(ze,{})]}),e.jsx(He,{...n,children:t})]}):t,e.jsx(K,{nonce:s}),e.jsx(J,{nonce:s})]})]})}const nt=B(function(){return e.jsx(Q,{})}),it=z(function(){var i;const s=Z();let n="Unknown error",r=500;return X(s)?(n=((i=s==null?void 0:s.data)==null?void 0:i.message)??s.data,r=s.status):s instanceof Error&&(n=s.message),e.jsxs("div",{className:"route-error",children:[e.jsx("h1",{children:"Oops"}),e.jsx("h2",{children:r}),n&&e.jsx("fieldset",{children:e.jsx("pre",{children:n})})]})});export{it as E,rt as L,et as f,st as l,nt as r,tt as s,Ae as u};
